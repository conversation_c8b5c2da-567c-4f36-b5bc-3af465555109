#!/bin/bash

# Bare-bones database connection test for Digital Ocean App Platform
# This script tests database connectivity using the environment variables
# that Digital Ocean automatically provides

echo "🔍 DIGITAL OCEAN DATABASE CONNECTION TEST"
echo "=========================================="

# Display all environment variables for debugging
echo ""
echo "📋 ALL ENVIRONMENT VARIABLES:"
env | sort

echo ""
echo "🔍 DATABASE-RELATED ENVIRONMENT VARIABLES:"
env | grep -i -E "(database|db|postgres)" | sort

echo ""
echo "🔌 TESTING DATABASE CONNECTION..."

# Test 1: Use DATABASE_URL if available (auto-injected by DO)
if [ ! -z "$DATABASE_URL" ]; then
    echo "✅ Found DATABASE_URL environment variable"
    echo "🔗 DATABASE_URL: $DATABASE_URL"
    
    echo "🧪 Testing connection with DATABASE_URL..."
    if psql "$DATABASE_URL" -c "SELECT version();" 2>/dev/null; then
        echo "✅ SUCCESS: Connected using DATABASE_URL!"
        echo "🎉 Database connection is working!"
    else
        echo "❌ FAILED: Could not connect using DATABASE_URL"
        echo "🔍 Detailed error:"
        psql "$DATABASE_URL" -c "SELECT version();" 2>&1
    fi
else
    echo "⚠️  DATABASE_URL not found"
fi

echo ""

# Test 2: Use individual database environment variables if available
if [ ! -z "$DB_HOST" ] && [ ! -z "$DB_USER" ] && [ ! -z "$DB_PASSWORD" ] && [ ! -z "$DB_NAME" ]; then
    echo "✅ Found individual database environment variables"
    echo "🔗 DB_HOST: $DB_HOST"
    echo "🔗 DB_USER: $DB_USER"
    echo "🔗 DB_NAME: $DB_NAME"
    echo "🔗 DB_PORT: ${DB_PORT:-5432}"
    
    # Construct connection string
    DB_CONNECTION="postgresql://$DB_USER:$DB_PASSWORD@$DB_HOST:${DB_PORT:-5432}/$DB_NAME"
    
    echo "🧪 Testing connection with individual variables..."
    if psql "$DB_CONNECTION" -c "SELECT version();" 2>/dev/null; then
        echo "✅ SUCCESS: Connected using individual variables!"
        echo "🎉 Database connection is working!"
    else
        echo "❌ FAILED: Could not connect using individual variables"
        echo "🔍 Detailed error:"
        psql "$DB_CONNECTION" -c "SELECT version();" 2>&1
    fi
else
    echo "⚠️  Individual database variables not found"
fi

echo ""

# Test 3: Try different SSL modes if connection fails
if [ ! -z "$DATABASE_URL" ]; then
    echo "🔒 TESTING DIFFERENT SSL MODES..."
    
    # Extract base URL without SSL parameters
    BASE_URL=$(echo "$DATABASE_URL" | sed 's/?.*$//')
    
    for sslmode in disable allow prefer require; do
        echo "🧪 Testing with sslmode=$sslmode..."
        TEST_URL="${BASE_URL}?sslmode=$sslmode"
        
        if psql "$TEST_URL" -c "SELECT version();" 2>/dev/null; then
            echo "✅ SUCCESS with sslmode=$sslmode!"
            echo "🎯 WORKING CONNECTION STRING: $TEST_URL"
            break
        else
            echo "❌ Failed with sslmode=$sslmode"
        fi
    done
fi

echo ""
echo "🏁 DATABASE CONNECTION TEST COMPLETE"
echo "=========================================="

# Start a simple HTTP server for health checks
echo "🌐 Starting HTTP server for health checks on port 8080..."

# Create a simple HTTP response function
http_response() {
    echo -e "HTTP/1.1 200 OK\r\nContent-Type: text/plain\r\nContent-Length: 2\r\n\r\nOK"
}

# Simple HTTP server using netcat (if available) or just sleep
if command -v nc >/dev/null 2>&1; then
    while true; do
        echo "$(http_response)" | nc -l -p 8080 -q 1
    done
else
    # Fallback: just keep the container running
    echo "⚠️  netcat not available, keeping container alive..."
    while true; do
        sleep 60
        echo "🔄 Container still running... $(date)"
    done
fi
