"""
Health check endpoint for DigitalOcean App Platform monitoring.
"""
from django.http import HttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.cache import never_cache
from django.conf import settings
import logging
import traceback
import os

# Set up logging
logger = logging.getLogger(__name__)

@csrf_exempt
@never_cache
def health_check(request):
    """
    Ultra-minimal health check that bypasses Django security for debugging.
    """
    try:
        # Log everything for debugging
        logger.info("🏥 Health check started")
        logger.info(f"   Request method: {request.method}")
        logger.info(f"   Request path: {request.path}")
        logger.info(f"   Request host: {request.get_host()}")
        logger.info(f"   Request headers: {dict(request.headers)}")
        logger.info(f"   ALLOWED_HOSTS: {settings.ALLOWED_HOSTS}")
        logger.info(f"   DEBUG: {settings.DEBUG}")
        logger.info(f"   Environment ALLOWED_HOSTS: {os.environ.get('ALLOWED_HOSTS', 'NOT_SET')}")

        # Return the most basic possible response to avoid any Django processing issues
        return HttpResponse('OK', content_type='text/plain', status=200)

    except Exception as e:
        logger.error(f"❌ Health check failed: {str(e)}")
        logger.error(f"❌ Exception type: {type(e).__name__}")
        logger.error(f"❌ Traceback: {traceback.format_exc()}")

        # Return error with details
        try:
            error_details = f"ERROR: {str(e)} | Host: {request.get_host() if hasattr(request, 'get_host') else 'unknown'}"
            return HttpResponse(error_details, content_type='text/plain', status=500)
        except:
            return HttpResponse('CRITICAL ERROR', content_type='text/plain', status=500)