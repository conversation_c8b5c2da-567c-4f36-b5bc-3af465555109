name: goali-app
region: nyc

services:
- name: backend
  source_dir: /
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  dockerfile_path: backend/Dockerfile.production
  http_port: 8000
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  routes:
  - path: /api
  - path: /admin
  - path: /health
  - path: /ws
  health_check:
    http_path: /health/
    initial_delay_seconds: 120
    period_seconds: 30
    timeout_seconds: 15
    success_threshold: 1
    failure_threshold: 5
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DEBUG
    value: "False"
  - key: ALLOWED_HOSTS
    value: "goali-app-*.ondigitalocean.app,localhost,127.0.0.1,10.*,172.*,192.*"
  # DATABASE_URL with SSL disabled (preserving bindable variable progress)
  # Using manual URL with correct credentials but sslmode=disable
  - key: DATABASE_URL
    value: postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=allow
  - key: REDIS_URL
    value: redis://127.0.0.1:6379/0
  - key: CELERY_BROKER_URL
    value: redis://127.0.0.1:6379/0
  - key: CELERY_RESULT_BACKEND
    value: redis://127.0.0.1:6379/1
  - key: SECRET_KEY
    value: ${SECRET_KEY}
    type: SECRET
  - key: MISTRAL_API_KEY
    value: ${MISTRAL_API_KEY}
    type: SECRET
  - key: SPACES_ACCESS_KEY
    value: ${SPACES_ACCESS_KEY}
    type: SECRET
  - key: SPACES_SECRET_KEY
    value: ${SPACES_SECRET_KEY}
    type: SECRET
  - key: SPACES_BUCKET_NAME
    value: goali-static-assets
  - key: SPACES_REGION
    value: nyc3
  - key: SPACES_CDN_ENDPOINT
    value: goali-static-assets.nyc3.cdn.digitaloceanspaces.com
  - key: DEFAULT_LLM_MODEL_NAME
    value: mistral-small-latest
  - key: DEFAULT_LLM_TEMPERATURE
    value: "0.7"
  - key: GOALI_DEFAULT_EXECUTION_MODE
    value: production
  - key: BUILD_TIME_SECRET_KEY
    value: build-time-secret-key-not-for-production
  # Database should now work with auto-injection - remove skip migration checks
  - key: SKIP_MIGRATION_CHECKS
    value: "False"

# Celery worker - re-enabled now that database connectivity is working
workers:
- name: celery-worker
  source_dir: /
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  dockerfile_path: backend/Dockerfile.production
  run_command: celery -A config worker --loglevel=info --concurrency=2
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-0.5gb
  envs:
  - key: DJANGO_SETTINGS_MODULE
    value: config.settings.production
  - key: DEBUG
    value: "False"
  - key: ALLOWED_HOSTS
    value: "goali-app-*.ondigitalocean.app,localhost,127.0.0.1,10.*,172.*,192.*"
  # DATABASE_URL with SSL disabled (preserving bindable variable progress)
  # Using manual URL with correct credentials but sslmode=disable
  - key: DATABASE_URL
    value: postgresql://doadmin:<EMAIL>:25060/defaultdb?sslmode=allow
  - key: REDIS_URL
    value: redis://127.0.0.1:6379/0
  - key: CELERY_BROKER_URL
    value: redis://127.0.0.1:6379/0
  - key: CELERY_RESULT_BACKEND
    value: redis://127.0.0.1:6379/1
  - key: SECRET_KEY
    value: ${SECRET_KEY}
    type: SECRET
  - key: MISTRAL_API_KEY
    value: ${MISTRAL_API_KEY}
    type: SECRET

# Remove database component - using direct connection to existing managed database
# databases:
# - name: db
#   engine: PG
#   version: "15"
#   production: true
#   cluster_name: goali-db-cluster

static_sites:
- name: frontend
  source_dir: frontend
  github:
    repo: elgui/goali
    branch: gguine/prod-do
    deploy_on_push: true
  build_command: npm install && npm run build
  output_dir: dist
  index_document: index.html
  error_document: index.html
  routes:
  - path: /
  envs:
  - key: VITE_API_URL
    value: https://goali-app-backend.ondigitalocean.app/api
  - key: VITE_WS_URL
    value: wss://goali-app-backend.ondigitalocean.app/ws

# Connect to managed database - Digital Ocean will auto-inject DATABASE_URL
# Following official Digital Ocean example pattern exactly (by-the-book approach)
databases:
- name: goali-db-nyc
  engine: PG
  production: true
  cluster_name: goali-db-nyc
  db_name: defaultdb
  db_user: doadmin
  version: "16"